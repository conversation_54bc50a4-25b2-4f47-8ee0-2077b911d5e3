'use client';

import { useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { usePurchases } from '@/hooks/use-purchases';

interface PurchaseHistoryProps {
  customerId: string;
}

export function PurchaseHistory({ customerId }: PurchaseHistoryProps) {
  const {
    purchases,
    loading,
    error,
    pagination,
    fetchPurchases,
  } = usePurchases();

  useEffect(() => {
    fetchPurchases(customerId);
  }, [customerId, fetchPurchases]);

  if (loading && purchases.length === 0) {
    return <PurchaseHistorySkeleton />;
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <p>Error loading purchase history: {error}</p>
            <Button
              variant="outline"
              onClick={() => fetchPurchases(customerId)}
              className="mt-2"
            >
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (purchases.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <p>No purchase history found.</p>
            <p className="text-sm">Record the first purchase to get started.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'completed':
        return 'default';
      case 'pending':
        return 'secondary';
      case 'failed':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  return (
    <div className="space-y-4">
      <div className="text-sm text-muted-foreground">
        {pagination.total} purchase{pagination.total !== 1 ? 's' : ''} found
      </div>

      <div className="space-y-3 max-h-96 overflow-y-auto">
        {purchases.map((purchase) => (
          <Card key={purchase.id}>
            <CardContent className="pt-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">
                      {purchase.sessionsPurchased} session{purchase.sessionsPurchased !== 1 ? 's' : ''}
                    </span>
                    <Badge variant={getStatusBadgeVariant(purchase.paymentStatus)}>
                      {purchase.paymentStatus}
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {new Date(purchase.purchaseDate).toLocaleDateString()} at{' '}
                    {new Date(purchase.purchaseDate).toLocaleTimeString()}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">${purchase.amountPaid}</div>
                  <div className="text-sm text-muted-foreground">
                    ${(parseFloat(purchase.amountPaid) / purchase.sessionsPurchased).toFixed(2)}/session
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {pagination.hasMore && (
        <div className="text-center">
          <Button
            variant="outline"
            onClick={() => fetchPurchases(customerId, { offset: purchases.length })}
            disabled={loading}
          >
            {loading ? 'Loading...' : 'Load More'}
          </Button>
        </div>
      )}
    </div>
  );
}

function PurchaseHistorySkeleton() {
  return (
    <div className="space-y-3">
      {Array.from({ length: 3 }).map((_, i) => (
        <Card key={i}>
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-5 w-16" />
                </div>
                <Skeleton className="h-3 w-32" />
              </div>
              <div className="text-right space-y-2">
                <Skeleton className="h-4 w-12" />
                <Skeleton className="h-3 w-16" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
