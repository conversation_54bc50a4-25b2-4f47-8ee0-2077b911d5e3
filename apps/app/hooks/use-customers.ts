'use client';

import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import type {
  CustomerResponse,
  CreateCustomerInput,
  UpdateCustomerInput,
  CustomerQueryInput,
  PaginatedResponse,
} from '@/lib/validations';

interface UseCustomersState {
  customers: CustomerResponse[];
  loading: boolean;
  error: string | null;
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

export function useCustomers() {
  const [state, setState] = useState<UseCustomersState>({
    customers: [],
    loading: false,
    error: null,
    pagination: {
      total: 0,
      limit: 20,
      offset: 0,
      hasMore: false,
    },
  });

  const fetchCustomers = useCallback(async (params?: Partial<CustomerQueryInput>) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const searchParams = new URLSearchParams();
      
      if (params?.search) searchParams.set('search', params.search);
      if (params?.limit) searchParams.set('limit', params.limit.toString());
      if (params?.offset) searchParams.set('offset', params.offset.toString());
      if (params?.sortBy) searchParams.set('sortBy', params.sortBy);
      if (params?.sortOrder) searchParams.set('sortOrder', params.sortOrder);

      const response = await fetch(`/api/customers?${searchParams.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch customers');
      }

      const data: PaginatedResponse<CustomerResponse> = await response.json();

      setState(prev => ({
        ...prev,
        customers: params?.offset ? [...prev.customers, ...data.data] : data.data,
        pagination: data.pagination,
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'An error occurred',
        loading: false,
      }));
    }
  }, []);

  const createCustomer = useCallback(async (customerData: CreateCustomerInput) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(customerData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create customer');
      }

      const newCustomer: CustomerResponse = await response.json();

      setState(prev => ({
        ...prev,
        customers: [newCustomer, ...prev.customers],
        pagination: {
          ...prev.pagination,
          total: prev.pagination.total + 1,
        },
        loading: false,
      }));

      toast.success('Customer created successfully');
      return newCustomer;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      setState(prev => ({
        ...prev,
        error: errorMessage,
        loading: false,
      }));
      toast.error(errorMessage);
      throw error;
    }
  }, []);

  const updateCustomer = useCallback(async (id: string, customerData: UpdateCustomerInput) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch(`/api/customers/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(customerData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update customer');
      }

      const updatedCustomer: CustomerResponse = await response.json();

      setState(prev => ({
        ...prev,
        customers: prev.customers.map(customer =>
          customer.id === id ? updatedCustomer : customer
        ),
        loading: false,
      }));

      toast.success('Customer updated successfully');
      return updatedCustomer;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      setState(prev => ({
        ...prev,
        error: errorMessage,
        loading: false,
      }));
      toast.error(errorMessage);
      throw error;
    }
  }, []);

  const deleteCustomer = useCallback(async (id: string) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch(`/api/customers/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete customer');
      }

      setState(prev => ({
        ...prev,
        customers: prev.customers.filter(customer => customer.id !== id),
        pagination: {
          ...prev.pagination,
          total: prev.pagination.total - 1,
        },
        loading: false,
      }));

      toast.success('Customer deleted successfully');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      setState(prev => ({
        ...prev,
        error: errorMessage,
        loading: false,
      }));
      toast.error(errorMessage);
      throw error;
    }
  }, []);

  return {
    ...state,
    fetchCustomers,
    createCustomer,
    updateCustomer,
    deleteCustomer,
  };
}
