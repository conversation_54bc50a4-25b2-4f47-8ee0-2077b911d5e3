'use client';

import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import type {
  PurchaseResponse,
  CreatePurchaseInput,
  PurchaseQueryInput,
  PaginatedResponse,
} from '@/lib/validations';

interface UsePurchasesState {
  purchases: PurchaseResponse[];
  loading: boolean;
  error: string | null;
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

export function usePurchases() {
  const [state, setState] = useState<UsePurchasesState>({
    purchases: [],
    loading: false,
    error: null,
    pagination: {
      total: 0,
      limit: 20,
      offset: 0,
      hasMore: false,
    },
  });

  const fetchPurchases = useCallback(async (customerId: string, params?: Partial<PurchaseQueryInput>) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const searchParams = new URLSearchParams();
      
      if (params?.limit) searchParams.set('limit', params.limit.toString());
      if (params?.offset) searchParams.set('offset', params.offset.toString());
      if (params?.sortBy) searchParams.set('sortBy', params.sortBy);
      if (params?.sortOrder) searchParams.set('sortOrder', params.sortOrder);

      const response = await fetch(`/api/customers/${customerId}/purchases?${searchParams.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch purchases');
      }

      const data: PaginatedResponse<PurchaseResponse> = await response.json();

      setState(prev => ({
        ...prev,
        purchases: params?.offset ? [...prev.purchases, ...data.data] : data.data,
        pagination: data.pagination,
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'An error occurred',
        loading: false,
      }));
    }
  }, []);

  const createPurchase = useCallback(async (customerId: string, purchaseData: CreatePurchaseInput) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch(`/api/customers/${customerId}/purchases`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(purchaseData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create purchase');
      }

      const newPurchase: PurchaseResponse = await response.json();

      setState(prev => ({
        ...prev,
        purchases: [newPurchase, ...prev.purchases],
        pagination: {
          ...prev.pagination,
          total: prev.pagination.total + 1,
        },
        loading: false,
      }));

      toast.success('Purchase recorded successfully');
      return newPurchase;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      setState(prev => ({
        ...prev,
        error: errorMessage,
        loading: false,
      }));
      toast.error(errorMessage);
      throw error;
    }
  }, []);

  return {
    ...state,
    fetchPurchases,
    createPurchase,
  };
}
